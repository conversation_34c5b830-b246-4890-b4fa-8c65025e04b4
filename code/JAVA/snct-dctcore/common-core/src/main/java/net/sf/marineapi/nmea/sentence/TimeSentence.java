/* 
 * TimeSentence.java
 * Copyright (C) 2010 <PERSON><PERSON>
 * 
 * This file is part of Java Marine API.
 * <http://ktuukkan.github.io/marine-api/>
 * 
 * Java Marine API is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, either version 3 of the License, or (at your
 * option) any later version.
 * 
 * Java Marine API is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public License
 * for more details.
 * 
 * You should have received a copy of the GNU Lesser General Public License
 * along with Java Marine API. If not, see <http://www.gnu.org/licenses/>.
 */
package net.sf.marineapi.nmea.sentence;

import net.sf.marineapi.nmea.sentence.Sentence;
import net.sf.marineapi.nmea.util.Time;

/**
 * Interface for sentences that provide UTC time. Notice that some sentences
 * contain only UTC time, while others may provide also date.
 * 
 * <AUTHOR>
 * @see net.sf.marineapi.nmea.sentence.DateSentence
 */
public interface TimeSentence extends Sentence {

	/**
	 * Get the time of day.
	 * 
	 * @return Time
	 */
	Time getTime();

	/**
	 * Set the time of day.
	 * 
	 * @param t Time to set
	 */
	void setTime(Time t);
}
