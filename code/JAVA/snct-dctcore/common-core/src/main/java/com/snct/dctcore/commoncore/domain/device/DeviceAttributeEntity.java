package com.snct.dctcore.commoncore.domain.device;


import com.snct.dctcore.commoncore.domain.BaseEntity2;

/**
 * class
 *
 * <AUTHOR>
 */
public class DeviceAttributeEntity extends BaseEntity2 {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 设备类型
     */
    private Integer type;
    /**
     * 名称
     */
    private String name;

    /**
     * 标签
     */
    private String label;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

}
