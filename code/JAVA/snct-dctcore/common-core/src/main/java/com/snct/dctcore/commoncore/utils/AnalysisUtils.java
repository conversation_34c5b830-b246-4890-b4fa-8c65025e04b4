package com.snct.dctcore.commoncore.utils;

import com.snct.dctcore.commoncore.domain.engineroom.EngineroomData;

/**
 * <AUTHOR>
 * 解析操作工具类
 */
public class AnalysisUtils {

    public static String analysis(EngineroomData engineroomData) {
        return engineroomData.getStatus() + "|" + engineroomData.getSymbol() + "|" + engineroomData.getValue();
    }

    public static String analysisValue(EngineroomData engineroomData) {
        return engineroomData.getValue();
    }

    /**
     * 处理字符串数组，去除每个元素的前后空白
     *
     * @param values 原始字符串数组
     * @return 处理后的字符串数组
     */
    public static String[] valuesTrim(String[] values) {
        String[] result = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            result[i] = values[i].trim();
        }
        return result;
    }
}
