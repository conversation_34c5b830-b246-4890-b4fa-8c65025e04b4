package com.snct.dctcore.commoncore.domain.hbase.old;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @description: winch的@RMReW数据 逗号隔开
 * Example:@RMReW,4,0,0.00,0.00,0.00,7,0.00,0.00*52
 * @RMReW,<1>,<2>,<3>,<4>,<5>,<6>,<7>,<8>
 *
 * @author: rr
 * @create: 2020-06-15 15:22
 **/
@HBaseTable(tableName = "ns1:winch-15")
public class WinchHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /**
     * 1.绞车编码，1=CTD绞车，2=水文绞车，5=光电绞车，6=地质绞车
     */
    @Excel(name="绞车编码")
    @HBaseColumn(family = "i", qualifier = "w_c")
    private String winchCode;

    /**
     * 2.是否运行，0=停止运行，1=正在运行 ？
     */
    @Excel(name="是否运行")
    @HBaseColumn(family = "i", qualifier = "status")
    private String status;

    /**
     * 3. 当前缆长
     */
    @Excel(name="当前缆长")
    @HBaseColumn(family = "i", qualifier = "len")
    private String length;

    /**
     * 4.绞车张力
     */
    @Excel(name="绞车张力")
    @HBaseColumn(family = "i", qualifier = "ten")
    private String tension;

    /**
     * 5.绞车速度
     */
    @Excel(name="绞车速度")
    @HBaseColumn(family = "i", qualifier = "speed")
    private String speed;

    /**
     * 6. Alarm status
     */
    @HBaseColumn(family = "i", qualifier = "alar")
    private String alarmStatus;

    /**
     * 7.Block length
     */
    @HBaseColumn(family = "i", qualifier = "b_l")
    private String blockLength;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWinchCode() {
        return winchCode;
    }

    public void setWinchCode(String winchCode) {
        this.winchCode = winchCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLength() {
        return length;
    }

    public void setLength(String length) {
        this.length = length;
    }

    public String getTension() {
        return tension;
    }

    public void setTension(String tension) {
        this.tension = tension;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getAlarmStatus() {
        return alarmStatus;
    }

    public void setAlarmStatus(String alarmStatus) {
        this.alarmStatus = alarmStatus;
    }

    public String getBlockLength() {
        return blockLength;
    }

    public void setBlockLength(String blockLength) {
        this.blockLength = blockLength;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
