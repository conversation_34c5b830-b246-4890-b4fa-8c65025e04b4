package com.snct.dctcore.commoncore.domain;

/**
 * @ClassName: Point
 * @Description: 坐标点类
 * @author: wzewei
 * @date: 2025-08-21 09:30
 */
public class Point {
    
    /**
     * X坐标或经度
     */
    private double x;
    
    /**
     * Y坐标或纬度
     */
    private double y;
    
    public Point() {
    }
    
    public Point(double x, double y) {
        this.x = x;
        this.y = y;
    }
    
    public double getX() {
        return x;
    }
    
    public void setX(double x) {
        this.x = x;
    }
    
    public double getY() {
        return y;
    }
    
    public void setY(double y) {
        this.y = y;
    }
    
    /**
     * 获取经度（X坐标的别名）
     */
    public double getLongitude() {
        return x;
    }
    
    /**
     * 设置经度（X坐标的别名）
     */
    public void setLongitude(double longitude) {
        this.x = longitude;
    }
    
    /**
     * 获取纬度（Y坐标的别名）
     */
    public double getLatitude() {
        return y;
    }
    
    /**
     * 设置纬度（Y坐标的别名）
     */
    public void setLatitude(double latitude) {
        this.y = latitude;
    }
    
    @Override
    public String toString() {
        return "Point{" +
                "x=" + x +
                ", y=" + y +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Point point = (Point) obj;
        return Double.compare(point.x, x) == 0 && Double.compare(point.y, y) == 0;
    }
    
    @Override
    public int hashCode() {
        return Double.hashCode(x) * 31 + Double.hashCode(y);
    }
}
