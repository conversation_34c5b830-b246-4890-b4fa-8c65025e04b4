package com.snct.dctcore.commoncore.utils;

import com.snct.dctcore.commoncore.domain.ChannelInfo;
import io.netty.channel.ChannelHandlerContext;
import org.apache.commons.lang3.StringUtils;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName: ChannelInfoUtil
 * @Description: 通道信息管理
 * @author: wzewei
 * @date: 2025-09-02 17:35:05
 */
public class ChannelInfoUtil {

    private static Map<String, ChannelInfo> channelInfoMap = new ConcurrentHashMap<>();


    /**
     * 通过sn号，获取通道信息的KEY
     *
     * @param sn
     * @return
     */
    private static String getKeyBySn(String sn) {
        return "UDP_CHANNEL_" + sn;
    }

    /**
     * 更新pazu连接的通道信息
     *
     * @param sn   sn
     * @param ctx  udp实例
     * @param ip   IP
     * @param port 端口
     */
    public static void renewChannel(String sn, ChannelHandlerContext ctx, String ip, Integer port) {
        String key = getKeyBySn(sn);
        if (channelInfoMap.get(key) != null && channelInfoMap.get(key).getCtx().equals(ctx)
                && channelInfoMap.get(key).getIp().equals(ip) && channelInfoMap.get(key).getPort().equals(port)) {
            return;
        }
        ChannelInfo channelInfo = new ChannelInfo(ctx, ip, port);
        channelInfoMap.put(key, channelInfo);
    }

    public static Map<String, ChannelInfo> getChannels() {
        return channelInfoMap;
    }

    public static ChannelInfo getChannel(String sn) {
        if (StringUtils.isBlank(getKeyBySn(sn))) {
            return null;
        }

        return channelInfoMap.get(getKeyBySn(sn));
    }

    public static void removeChannel(String sn) {
        channelInfoMap.remove(getKeyBySn(sn));
    }
}