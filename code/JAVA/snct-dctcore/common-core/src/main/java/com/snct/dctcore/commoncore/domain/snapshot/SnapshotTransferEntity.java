package com.snct.dctcore.commoncore.domain.snapshot;

import com.snct.dctcore.commoncore.domain.BaseEntity2;

/**
 * 快照传输
 *
 * <AUTHOR>
 */
public class SnapshotTransferEntity extends BaseEntity2 {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 部门ID（岸上需要）
     */
    private Long deptId;

    private String sn;

    private String shipName;
    /**
     * 通道编号
     */
    private String channelCode;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 分辨率(640*360)
     */
    private String resolvingPower;

    /**
     * 传输间隔(单位：秒)
     */
    private Integer compartment;

    /**
     * 传输间隔--展示
     */
    private String compartmentStr;

    /**
     * 优先级
     */
    private Integer cost;
    /**
     * 存储状态
     */
    private Integer status;

    public Integer getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(Integer transferStatus) {
        this.transferStatus = transferStatus;
    }

    /**
     * 快照传输状态
     */
    private Integer transferStatus;

    /**
     * 连接状态
     * @return
     */
    private String connectStatus;

    /**
     * 连接状态-显示
     * @return
     */
    private String connectStatusStr;

    public Long getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Long operateTime) {
        this.operateTime = operateTime;
    }

    /**
     * 截屏时间
     */
    private Long operateTime;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 目录
     */
    private String directory;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDirectory() {
        return directory;
    }

    public void setDirectory(String directory) {
        this.directory = directory;
    }

    public String getConnectStatus() {
        return connectStatus;
    }

    public void setConnectStatus(String connectStatus) {
        this.connectStatus = connectStatus;
    }

    public String getConnectStatusStr() {
        if (this.connectStatus == null || this.connectStatus.equalsIgnoreCase("")) {
            return "连接失败";
        }
        if (this.connectStatus.equalsIgnoreCase("1")){
            return "连接成功";
        }
        return "连接失败";
    }

    public void setConnectStatusStr(String connectStatusStr) {
        this.connectStatusStr = connectStatusStr;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getResolvingPower() {
        return resolvingPower;
    }

    public void setResolvingPower(String resolvingPower) {
        this.resolvingPower = resolvingPower;
    }

    public Integer getCompartment() {
        return compartment;
    }

    public void setCompartment(Integer compartment) {
        this.compartment = compartment;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getCompartmentStr() {
        if (this.compartment == null) {
            return "";
        }
        if (this.compartment >= 60) {
            return (this.compartment / 60) + "分钟";
        } else {
            return this.compartment + "秒钟";
        }
    }

    public void setCompartmentStr(String compartmentStr) {
        this.compartmentStr = compartmentStr;
    }
}
