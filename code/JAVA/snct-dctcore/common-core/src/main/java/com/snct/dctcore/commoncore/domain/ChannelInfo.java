package com.snct.dctcore.commoncore.domain;

import io.netty.channel.ChannelHandlerContext;

/**
 * @ClassName: ChannelInfo
 * @Description: 通道信息
 * @author: wzewei
 * @date: 2025-09-02 17:34:23
 */
public class ChannelInfo {
    private ChannelHandlerContext ctx;
    private String ip;
    private Integer port;

    public ChannelInfo() {
    }

    public ChannelInfo(ChannelHandlerContext ctx, String ip, Integer port) {
        this.ctx = ctx;
        this.ip = ip;
        this.port = port;
    }

    public ChannelHandlerContext getCtx() {
        return ctx;
    }

    public void setCtx(ChannelHandlerContext ctx) {
        this.ctx = ctx;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }
}