package com.snct.dctcore.commoncore.domain.hbase.old;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @description:  罗经数据 1秒一组
 * @author: rr
 * @create: 2020-06-03 10:34
 **/
@HBaseTable
public class CompassHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;
    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /**
     * 当前罗经数据
     */
    @Excel(name="当前罗经数据")
    @HBaseColumn(family = "i", qualifier = "hehdt")
    private String hehdt;

    /**
     * 转向速率  Rate Of Turn负数表示向左舷转
     */
    @Excel(name="转向速率")
    @HBaseColumn(family = "i", qualifier = "herot")
    private String herot;



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHehdt() {
        return hehdt;
    }

    public void setHehdt(String hehdt) {
        this.hehdt = hehdt;
    }

    public String getHerot() {
        return herot;
    }

    public void setHerot(String herot) {
        this.herot = herot;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }


}
