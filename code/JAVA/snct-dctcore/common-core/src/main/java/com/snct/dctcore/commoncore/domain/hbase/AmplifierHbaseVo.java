package com.snct.dctcore.commoncore.domain.hbase;

import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;

/**
 * @ClassName: AmplifierHbaseVo
 * @Description: 功放数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 14:50
 */
public class AmplifierHbaseVo {

    /** ID */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;

    /** 衰减值 */
    @Excel(name = "衰减值")
    @HBaseColumn(family = "i", qualifier = "decay")
    private String decay;

    /** 温度 */
    @Excel(name = "温度")
    @HBaseColumn(family = "i", qualifier = "temp")
    private String temp;

    /** 输出功率 */
    @Excel(name = "输出功率")
    @HBaseColumn(family = "i", qualifier = "o_power")
    private String outPower;

    /** 设备状态 */
    @Excel(name = "设备状态")
    @HBaseColumn(family = "i", qualifier = "status")
    private String bucStatus;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public void setDecay(String decay)
    {
        this.decay = decay;
    }

    public String getDecay()
    {
        return decay;
    }

    public void setTemp(String temp)
    {
        this.temp = temp;
    }

    public String getTemp()
    {
        return temp;
    }

    public void setOutPower(String outPower)
    {
        this.outPower = outPower;
    }

    public String getOutPower()
    {
        return outPower;
    }

    public void setBucStatus(String bucStatus)
    {
        this.bucStatus = bucStatus;
    }

    public String getBucStatus()
    {
        return bucStatus;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}

