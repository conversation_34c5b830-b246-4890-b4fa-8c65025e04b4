package com.snct.dctcore.commoncore.domain.hbase.old;

import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @description: sb21的scan数据 10秒一组
 * Example: <Sbe21Scan index='20938'><Field0>20939</Field0><Field1>29.96138</Field1></Sbe21Scan>
 * <Sbe21Scan index='(1)'><Field0>(2)</Field0><Field1>(3)</Field1><Field2>(4)</Field2><Field3>(5)</Field3><Field4>(6)</Field4><Field5>(7)</Field5><Field6>(8)</Field6></Sbe21Scan>
 * @author: rr
 * @create: 2020-06-10 09:39
 **/
@HBaseTable
public class Sbe21HbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /**
     * 1. 数据标识0，注意这个字段外有单引号 ‘  ’
     */
    @HBaseColumn(family = "i", qualifier = "dm")
    private String dataMark;
    /**
     * 2.累加数据标识1，数值上等于数据标识0加1
     */
    @HBaseColumn(family = "i", qualifier = "ad")
    private String accumulatedData;

    /**
     * SBE21温度
     */
    @Excel(name="SBE21温度")
    @HBaseColumn(family = "i", qualifier = "temp")
    private String temperature;

    /**
     * 原位温度
     */
    @Excel(name="原位温度")
    @HBaseColumn(family = "i", qualifier = "s_t")
    private String situTemperature;

    /**
     * 盐度
     */
    @Excel(name="盐度")
    @HBaseColumn(family = "i", qualifier = "sali")
    private String salinity;

    /**
     * 溶解氧
     */
    @Excel(name="溶解氧")
    @HBaseColumn(family = "i", qualifier = "diss")
    private String dissolvedOxygen;

    /**
     * 叶绿素
     */
    @Excel(name="叶绿素")
    @HBaseColumn(family = "i", qualifier = "chlo")
    private String chlorophyll;

    /**
     * 浊度
     */
    @Excel(name="浊度")
    @HBaseColumn(family = "i", qualifier = "turb")
    private String turbidity;

    /**
     * 纬度
     */
    @HBaseColumn(family = "i", qualifier = "i_l_a")
    private String latitude;
    /**
     * 经度
     */
    @HBaseColumn(family = "i", qualifier = "i_l_o")
    private String longitude;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getSituTemperature() {
        return situTemperature;
    }

    public void setSituTemperature(String situTemperature) {
        this.situTemperature = situTemperature;
    }

    public String getSalinity() {
        return salinity;
    }

    public void setSalinity(String salinity) {
        this.salinity = salinity;
    }

    public String getDissolvedOxygen() {
        return dissolvedOxygen;
    }

    public void setDissolvedOxygen(String dissolvedOxygen) {
        this.dissolvedOxygen = dissolvedOxygen;
    }

    public String getChlorophyll() {
        return chlorophyll;
    }

    public void setChlorophyll(String chlorophyll) {
        this.chlorophyll = chlorophyll;
    }

    public String getTurbidity() {
        return turbidity;
    }

    public void setTurbidity(String turbidity) {
        this.turbidity = turbidity;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getDataMark() {
        return dataMark;
    }

    public void setDataMark(String dataMark) {
        this.dataMark = dataMark;
    }

    public String getAccumulatedData() {
        return accumulatedData;
    }

    public void setAccumulatedData(String accumulatedData) {
        this.accumulatedData = accumulatedData;
    }
}
