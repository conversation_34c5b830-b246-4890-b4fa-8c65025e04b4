package com.snct.dctcore.commoncore.utils;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.regex.Pattern;

/**
 * @ClassName: DistanceUtils
 * @Description: GPS坐标转换工具类 (用于处理NMEA格式的GPS坐标转换为标准十进制度数格式)
 * @author: wzewei
 * @date: 2025-08-21 18:00
 */
public class GpsCoordinateUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(GpsCoordinateUtils.class);
    
    // NMEA格式坐标的正则表达式 (DDMM.MMMMM 或 DDDMM.MMMMM)
    private static final Pattern NMEA_LAT_PATTERN = Pattern.compile("^\\d{4}\\.\\d+$");  // 纬度：DDMM.MMMMM
    private static final Pattern NMEA_LON_PATTERN = Pattern.compile("^\\d{5}\\.\\d+$");  // 经度：DDDMM.MMMMM
    
    // 标准十进制度数格式的正则表达式
    private static final Pattern DECIMAL_PATTERN = Pattern.compile("^-?\\d{1,3}\\.\\d+$");
    
    /**
     * 转换纬度坐标
     * 
     * @param latitude 纬度值（可能是NMEA格式或十进制格式）
     * @param hemisphere 半球标识（N/S）
     * @return 标准十进制度数格式的纬度
     */
    public static String convertLatitude(String latitude, String hemisphere) {
        if (StringUtils.isBlank(latitude) || "null".equals(latitude)) {
            return null;
        }
        
        try {
            // 去除空格
            latitude = latitude.trim();
            
            // 检查是否已经是十进制格式
            if (DECIMAL_PATTERN.matcher(latitude).matches()) {
                double lat = Double.parseDouble(latitude);
                // 验证纬度范围
                if (lat >= -90 && lat <= 90) {
                    return formatCoordinate(lat);
                }
            }
            
            // 检查是否是NMEA格式
            if (NMEA_LAT_PATTERN.matcher(latitude).matches()) {
                return convertNmeaLatitude(latitude, hemisphere);
            }
            
            logger.warn("无法识别的纬度格式: {}", latitude);
            return null;
            
        } catch (Exception e) {
            logger.error("纬度转换失败: {}, 错误: {}", latitude, e.getMessage());
            return null;
        }
    }
    
    /**
     * 转换经度坐标
     * 
     * @param longitude 经度值（可能是NMEA格式或十进制格式）
     * @param hemisphere 半球标识（E/W）
     * @return 标准十进制度数格式的经度
     */
    public static String convertLongitude(String longitude, String hemisphere) {
        if (StringUtils.isBlank(longitude) || "null".equals(longitude)) {
            return null;
        }
        
        try {
            // 去除空格
            longitude = longitude.trim();
            
            // 检查是否已经是十进制格式
            if (DECIMAL_PATTERN.matcher(longitude).matches()) {
                double lon = Double.parseDouble(longitude);
                // 验证经度范围
                if (lon >= -180 && lon <= 180) {
                    return formatCoordinate(lon);
                }
            }
            
            // 检查是否是NMEA格式
            if (NMEA_LON_PATTERN.matcher(longitude).matches()) {
                return convertNmeaLongitude(longitude, hemisphere);
            }
            
            logger.warn("无法识别的经度格式: {}", longitude);
            return null;
            
        } catch (Exception e) {
            logger.error("经度转换失败: {}, 错误: {}", longitude, e.getMessage());
            return null;
        }
    }
    
    /**
     * 转换NMEA格式的纬度
     * 
     * @param nmeaLatitude NMEA格式纬度 (DDMM.MMMMM)
     * @param hemisphere 半球标识（N/S）
     * @return 十进制度数格式的纬度
     */
    private static String convertNmeaLatitude(String nmeaLatitude, String hemisphere) {
        // NMEA纬度格式：DDMM.MMMMM
        // 例如：2233.64056 表示 22度33.64056分
        
        double nmeaValue = Double.parseDouble(nmeaLatitude);
        
        // 提取度数部分（前两位）
        int degrees = (int) (nmeaValue / 100);
        
        // 提取分钟部分
        double minutes = nmeaValue - (degrees * 100);
        
        // 转换为十进制度数
        double decimalDegrees = degrees + (minutes / 60.0);
        
        // 根据半球调整符号
        if ("S".equalsIgnoreCase(hemisphere)) {
            decimalDegrees = -decimalDegrees;
        }
        
        // 验证纬度范围
        if (decimalDegrees < -90 || decimalDegrees > 90) {
            logger.warn("纬度超出有效范围: {}", decimalDegrees);
            return null;
        }
        
        return formatCoordinate(decimalDegrees);
    }
    
    /**
     * 转换NMEA格式的经度
     * 
     * @param nmeaLongitude NMEA格式经度 (DDDMM.MMMMM)
     * @param hemisphere 半球标识（E/W）
     * @return 十进制度数格式的经度
     */
    private static String convertNmeaLongitude(String nmeaLongitude, String hemisphere) {
        // NMEA经度格式：DDDMM.MMMMM
        // 例如：11405.56124 表示 114度05.56124分
        
        double nmeaValue = Double.parseDouble(nmeaLongitude);
        
        // 提取度数部分（前三位）
        int degrees = (int) (nmeaValue / 100);
        
        // 提取分钟部分
        double minutes = nmeaValue - (degrees * 100);
        
        // 转换为十进制度数
        double decimalDegrees = degrees + (minutes / 60.0);
        
        // 根据半球调整符号
        if ("W".equalsIgnoreCase(hemisphere)) {
            decimalDegrees = -decimalDegrees;
        }
        
        // 验证经度范围
        if (decimalDegrees < -180 || decimalDegrees > 180) {
            logger.warn("经度超出有效范围: {}", decimalDegrees);
            return null;
        }
        
        return formatCoordinate(decimalDegrees);
    }
    
    /**
     * 格式化坐标，保留6位小数
     * 
     * @param coordinate 坐标值
     * @return 格式化后的坐标字符串
     */
    private static String formatCoordinate(double coordinate) {
        BigDecimal bd = new BigDecimal(coordinate);
        bd = bd.setScale(6, RoundingMode.HALF_UP);
        return bd.toString();
    }
    
    /**
     * 验证坐标是否有效
     * 
     * @param longitude 经度
     * @param latitude 纬度
     * @return 是否有效
     */
    public static boolean isValidCoordinate(String longitude, String latitude) {
        if (StringUtils.isBlank(longitude) || StringUtils.isBlank(latitude) ||
            "null".equals(longitude) || "null".equals(latitude)) {
            return false;
        }
        
        try {
            double lon = Double.parseDouble(longitude);
            double lat = Double.parseDouble(latitude);
            
            return lon >= -180 && lon <= 180 && lat >= -90 && lat <= 90;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 检测坐标格式类型
     * 
     * @param coordinate 坐标值
     * @return 格式类型：NMEA_LAT, NMEA_LON, DECIMAL, UNKNOWN
     */
    public static String detectCoordinateFormat(String coordinate) {
        if (StringUtils.isBlank(coordinate)) {
            return "UNKNOWN";
        }
        
        coordinate = coordinate.trim();
        
        if (NMEA_LAT_PATTERN.matcher(coordinate).matches()) {
            return "NMEA_LAT";
        } else if (NMEA_LON_PATTERN.matcher(coordinate).matches()) {
            return "NMEA_LON";
        } else if (DECIMAL_PATTERN.matcher(coordinate).matches()) {
            return "DECIMAL";
        } else {
            return "UNKNOWN";
        }
    }
}
