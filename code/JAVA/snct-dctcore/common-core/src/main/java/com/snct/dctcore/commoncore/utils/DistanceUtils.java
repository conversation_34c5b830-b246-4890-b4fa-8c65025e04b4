package com.snct.dctcore.commoncore.utils;

import com.snct.dctcore.commoncore.domain.Point;

/**
 * @ClassName: DistanceUtils
 * @Description: 距离计算工具类(提供多种距离计算方法，支持二维坐标和地理坐标)
 * @author: wzewei
 * @date: 2025-08-21 09:40
 */
public class DistanceUtils {

    /**
     * 地球半径（千米）
     */
    private static final double EARTH_RADIUS_KM = 6371.0; // 平均半径
    
    /**
     * 地球半径（米）
     */
    private static final double EARTH_RADIUS_M = 6371000.0;

    /**
     * 计算两个点之间的欧几里得距离（直线距离）
     *
     * @param point1 第一个点
     * @param point2 第二个点
     * @return 欧几里得距离
     */
    public static double calculateEuclideanDistance(Point point1, Point point2) {
        if (point1 == null || point2 == null) {
            throw new IllegalArgumentException("坐标点不能为空");
        }
        return calculateEuclideanDistance(point1.getX(), point1.getY(), point2.getX(), point2.getY());
    }

    /**
     * 计算两个坐标之间的欧几里得距离（直线距离）
     *
     * @param x1 第一个点的X坐标
     * @param y1 第一个点的Y坐标
     * @param x2 第二个点的X坐标
     * @param y2 第二个点的Y坐标
     * @return 欧几里得距离
     */
    public static double calculateEuclideanDistance(double x1, double y1, double x2, double y2) {
        double deltaX = x2 - x1;
        double deltaY = y2 - y1;
        return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    }

    /**
     * 计算两个点之间的曼哈顿距离（城市街区距离）
     *
     * @param point1 第一个点
     * @param point2 第二个点
     * @return 曼哈顿距离
     */
    public static double calculateManhattanDistance(Point point1, Point point2) {
        if (point1 == null || point2 == null) {
            throw new IllegalArgumentException("坐标点不能为空");
        }
        return calculateManhattanDistance(point1.getX(), point1.getY(), point2.getX(), point2.getY());
    }

    /**
     * 计算两个坐标之间的曼哈顿距离（城市街区距离）
     *
     * @param x1 第一个点的X坐标
     * @param y1 第一个点的Y坐标
     * @param x2 第二个点的X坐标
     * @param y2 第二个点的Y坐标
     * @return 曼哈顿距离
     */
    public static double calculateManhattanDistance(double x1, double y1, double x2, double y2) {
        return Math.abs(x2 - x1) + Math.abs(y2 - y1);
    }

    /**
     * 使用哈弗赛因公式计算地球表面两点间的距离（单位：千米）
     *
     * @param point1 第一个地理坐标点（经度，纬度）
     * @param point2 第二个地理坐标点（经度，纬度）
     * @return 地球表面距离（千米）
     */
    public static double calculateHaversineDistanceKm(Point point1, Point point2) {
        if (point1 == null || point2 == null) {
            throw new IllegalArgumentException("坐标点不能为空");
        }
        return calculateHaversineDistanceKm(point1.getLongitude(), point1.getLatitude(), 
                                          point2.getLongitude(), point2.getLatitude());
    }

    /**
     * 使用哈弗赛因公式计算地球表面两点间的距离（单位：千米）
     *
     * @param lon1 第一个点的经度
     * @param lat1 第一个点的纬度
     * @param lon2 第二个点的经度
     * @param lat2 第二个点的纬度
     * @return 地球表面距离（千米）
     */
    public static double calculateHaversineDistanceKm(double lon1, double lat1, double lon2, double lat2) {
        // 将角度转换为弧度
        double lat1Rad = Math.toRadians(lat1);
        double lat2Rad = Math.toRadians(lat2);
        double deltaLatRad = Math.toRadians(lat2 - lat1);
        double deltaLonRad = Math.toRadians(lon2 - lon1);

        // 哈弗赛因公式
        double a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return EARTH_RADIUS_KM * c;
    }

    /**
     * 使用哈弗赛因公式计算地球表面两点间的距离（单位：米）
     *
     * @param point1 第一个地理坐标点（经度，纬度）
     * @param point2 第二个地理坐标点（经度，纬度）
     * @return 地球表面距离（米）
     */
    public static double calculateHaversineDistanceM(Point point1, Point point2) {
        return calculateHaversineDistanceKm(point1, point2) * 1000;
    }

    /**
     * 使用哈弗赛因公式计算地球表面两点间的距离（单位：米）
     *
     * @param lon1 第一个点的经度
     * @param lat1 第一个点的纬度
     * @param lon2 第二个点的经度
     * @param lat2 第二个点的纬度
     * @return 地球表面距离（米）
     */
    public static double calculateHaversineDistanceM(double lon1, double lat1, double lon2, double lat2) {
        return calculateHaversineDistanceKm(lon1, lat1, lon2, lat2) * 1000;
    }

    /**
     * 使用文森特公式计算地球表面两点间的精确距离（单位：米）
     * 比哈弗赛因公式更精确，适用于长距离计算
     *
     * @param point1 第一个地理坐标点（经度，纬度）
     * @param point2 第二个地理坐标点（经度，纬度）
     * @return 地球表面距离（米）
     */
    public static double calculateVincentyDistance(Point point1, Point point2) {
        if (point1 == null || point2 == null) {
            throw new IllegalArgumentException("坐标点不能为空");
        }
        return calculateVincentyDistance(point1.getLongitude(), point1.getLatitude(),
                                       point2.getLongitude(), point2.getLatitude());
    }

    /**
     * 使用文森特公式计算地球表面两点间的精确距离（单位：米）
     * 比哈弗赛因公式更精确，适用于长距离计算
     *
     * @param lon1 第一个点的经度
     * @param lat1 第一个点的纬度
     * @param lon2 第二个点的经度
     * @param lat2 第二个点的纬度
     * @return 地球表面距离（米）
     */
    public static double calculateVincentyDistance(double lon1, double lat1, double lon2, double lat2) {
        // WGS84椭球参数
        double a = 6378137.0; // 长半轴
        double b = 6356752.314245; // 短半轴
        double f = 1 / 298.257223563; // 扁率

        double L = Math.toRadians(lon2 - lon1);
        double U1 = Math.atan((1 - f) * Math.tan(Math.toRadians(lat1)));
        double U2 = Math.atan((1 - f) * Math.tan(Math.toRadians(lat2)));
        double sinU1 = Math.sin(U1);
        double cosU1 = Math.cos(U1);
        double sinU2 = Math.sin(U2);
        double cosU2 = Math.cos(U2);

        double lambda = L;
        double lambdaP;
        int iterLimit = 100;
        double cosSqAlpha, sinSigma, cos2SigmaM, cosSigma, sigma;

        do {
            double sinLambda = Math.sin(lambda);
            double cosLambda = Math.cos(lambda);
            sinSigma = Math.sqrt((cosU2 * sinLambda) * (cosU2 * sinLambda) +
                    (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda) * (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda));

            if (sinSigma == 0) return 0; // 重合点

            cosSigma = sinU1 * sinU2 + cosU1 * cosU2 * cosLambda;
            sigma = Math.atan2(sinSigma, cosSigma);
            double sinAlpha = cosU1 * cosU2 * sinLambda / sinSigma;
            cosSqAlpha = 1 - sinAlpha * sinAlpha;
            cos2SigmaM = cosSigma - 2 * sinU1 * sinU2 / cosSqAlpha;

            if (Double.isNaN(cos2SigmaM)) cos2SigmaM = 0; // 赤道线

            double C = f / 16 * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
            lambdaP = lambda;
            lambda = L + (1 - C) * f * sinAlpha *
                    (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));
        } while (Math.abs(lambda - lambdaP) > 1e-12 && --iterLimit > 0);

        if (iterLimit == 0) {
            // 迭代失败，使用哈弗赛因公式作为备选
            return calculateHaversineDistanceM(lon1, lat1, lon2, lat2);
        }

        double uSq = cosSqAlpha * (a * a - b * b) / (b * b);
        double A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
        double B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));
        double deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) -
                B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));

        return b * A * (sigma - deltaSigma);
    }

    /**
     * 验证经纬度坐标是否有效
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 是否有效
     */
    public static boolean isValidCoordinate(double longitude, double latitude) {
        return longitude >= -180 && longitude <= 180 && latitude >= -90 && latitude <= 90;
    }

    /**
     * 验证坐标点是否有效
     *
     * @param point 坐标点
     * @return 是否有效
     */
    public static boolean isValidCoordinate(Point point) {
        if (point == null) {
            return false;
        }
        return isValidCoordinate(point.getLongitude(), point.getLatitude());
    }
}
