package com.snct.dctcore.commoncore.domain.hbase.old;

import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @description: Racdu1数据
 * Example:$RIRSA,01.1,A,,*04
 * $RIRSA,<1>,A,,*CS<CR><LF>
 * 逗号隔开
 * @author: cqf
 * @create: 2020-06-26 17:02
 **/
@HBaseTable
public class RacduHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;
    /**
     * 1.自动舵转向率
     */
    @Excel(name="自动舵转向率")
    @HBaseColumn(family = "i", qualifier = "a_r")
    private String autopilotRate;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAutopilotRate() {
        return autopilotRate;
    }

    public void setAutopilotRate(String autopilotRate) {
        this.autopilotRate = autopilotRate;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
