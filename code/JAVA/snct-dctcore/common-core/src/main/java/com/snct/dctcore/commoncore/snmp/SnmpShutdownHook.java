package com.snct.dctcore.commoncore.snmp;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;

/**
 * SNMP关闭钩子
 * 在应用关闭时负责清理SNMP连接
 * 
 * <AUTHOR>
 */
@Component
public class SnmpShutdownHook {
    
    private static final Logger logger = LoggerFactory.getLogger(SnmpShutdownHook.class);
    
    /**
     * 应用关闭前执行的方法
     * 关闭所有SNMP连接
     */
    @PreDestroy
    public void shutdown() {
        logger.info("应用关闭，正在清理SNMP连接...");
        SnmpUtils.closeAllConnections();
        logger.info("SNMP连接清理完成");
    }
} 