package com.snct.dctcore.commoncore.domain;

/**
 * @ClassName: KafkaMessage
 * @Description: kafka消息类
 * @author: wzewei
 * @date: 2025-09-05 09:28:24
 */
public class KafkaMessage {

	private String sn;

	private Integer type;

	private String code;

	private String msg;

	private Integer cost;

	private Long initialTime;

	public KafkaMessage() {
	}

	public KafkaMessage(String msg,Long initialTime) {
		this.msg = msg;
		this.initialTime = initialTime;
	}

	public KafkaMessage(String code, String msg, Integer cost, Long initialTime) {
		this.code = code;
		this.msg = msg;
		this.cost = cost;
		this.initialTime = initialTime;
	}

	public KafkaMessage(Integer type, String code, String msg, Integer cost, Long initialTime) {
		this.type = type;
		this.code = code;
		this.msg = msg;
		this.cost = cost;
		this.initialTime = initialTime;
	}

	public KafkaMessage(String sn, Integer type, String code, String msg, Integer cost, Long initialTime) {
		this.sn = sn;
		this.type = type;
		this.code = code;
		this.msg = msg;
		this.cost = cost;
		this.initialTime = initialTime;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public Integer getCost() {
		return cost;
	}

	public void setCost(Integer cost) {
		this.cost = cost;
	}

	public Long getInitialTime() {
		return initialTime;
	}

	public void setInitialTime(Long initialTime) {
		this.initialTime = initialTime;
	}
}