package com.snct.dctcore.commoncore.domain.engineroom;

import com.snct.dctcore.commoncore.utils.AnalysisUtils;

import java.util.Map;

/**
 * 造水机
 * <AUTHOR>
 */

public class FwGenerator{

    /**
     * 1号造水机盐度   11005
     */
    private String salinity1;

    /**
     * 2号造水机盐度  11011
     */
    private String salinity2;

    /**
     * 数据时间
     */
    private Long timeStamp;

    public String getSalinity1() {
        return salinity1;
    }

    public void setSalinity1(String salinity1) {
        this.salinity1 = salinity1;
    }

    public String getSalinity2() {
        return salinity2;
    }

    public void setSalinity2(String salinity2) {
        this.salinity2 = salinity2;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public FwGenerator(){

    }

    public FwGenerator(Map<String,EngineroomData> map){

        this.salinity1= AnalysisUtils.analysis(map.get("11005"));
        this.salinity2= AnalysisUtils.analysis(map.get("11011"));

    }

    public FwGenerator(String msg){

        String[] strbuff = msg.split(",",3);
        this.salinity1 = strbuff[0];
        this.salinity2 =strbuff[1];
        this.timeStamp = Long.parseLong(strbuff[2]);
    }

    @Override
    public String toString() {
        return "FwGenerator{" +
                "salinity1='" + salinity1 + '\'' +
                ", salinity2='" + salinity2 + '\'' +
                ", timeStamp=" + timeStamp +
                '}';
    }

    public String mergeSendStr() {
        StringBuffer sb = new StringBuffer();
        sb.append(salinity1 == null ? "" : salinity1).append(",");
        sb.append(salinity2 == null ? "" : salinity2).append(",");
        sb.append(timeStamp == null ? "" : timeStamp);
        return sb.toString();
    }
}