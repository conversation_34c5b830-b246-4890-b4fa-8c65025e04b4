package com.snct.dctcore.commoncore.analysis;


import cn.hutool.core.util.HexUtil;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.AmplifierHbaseVo;
import com.snct.dctcore.commoncore.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * @ClassName: AmplifierAnalysis
 * @Description: 功放数据解析
 * @author: wzewei
 * @date: 2025-09-05 10:49
 */
public class AmplifierAnalysis {

    protected static Logger logger = LoggerFactory.getLogger(AmplifierHbaseVo.class);

    public static AmplifierHbaseVo getAmplifierData(KafkaMessage kafkaMessage) {

        if (kafkaMessage == null || kafkaMessage.getMsg() == null || kafkaMessage.getMsg().isEmpty()) {
            return null;
        }
        AmplifierHbaseVo amplifierHbaseVo = null;

        try {
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() :
                    kafkaMessage.getInitialTime();
            String msg = kafkaMessage.getMsg();
            if (msg.length() >= 20) {
                String data = msg.substring(8, 20);

                amplifierHbaseVo = new AmplifierHbaseVo();
                amplifierHbaseVo.setInitialTime(Objects.requireNonNull(DateUtils.fetchWholeSecond(currentTime)).toString());
                amplifierHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime, "yyyy-MM-dd HH:mm:ss"));

                // 解析各项数据
                amplifierHbaseVo.setDecay(String.valueOf(HexUtil.hexToInt(data.substring(0, 2))));
                amplifierHbaseVo.setTemp(String.valueOf(HexUtil.hexToInt(data.substring(2, 4))));
                amplifierHbaseVo.setOutPower(String.valueOf(HexUtil.hexToInt(data.substring(4, 8)) / 10.0));
                amplifierHbaseVo.setBucStatus(String.valueOf(HexUtil.hexToInt(data.substring(10, 12))));
            }
        } catch (Exception e) {
            logger.error("功放数据解析出错, 消息内容: [{}], 异常详情:", kafkaMessage.getMsg(), e);
        }
        return amplifierHbaseVo;
    }
}
