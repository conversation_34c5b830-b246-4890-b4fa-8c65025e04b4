package com.snct.dctcore.commoncore.domain.hbase;


import com.snct.dctcore.commoncore.annotation.Excel;
import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.annotation.HBaseTable;

/**
 * @ClassName: AwsHbaseVo
 * @Description: GPS数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 13:39
 */
@HBaseTable
public class GpsHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;

    /**
     * UTC时间
     */
    @Excel(name = "UTC时间")
    @HBaseColumn(family = "i", qualifier = "utc")
    private String utcTime;

    /**
     * 纬度半球
     */
    @Excel(name = "纬度半球")
    @HBaseColumn(family = "i", qualifier = "lat_h")
    private String latitudeHemisphere;
    /**
     * 经度半球
     */
    @Excel(name = "经度半球")
    @HBaseColumn(family = "i", qualifier = "long_h")
    private String longitudeHemisphere;
    /**
     * 纬度
     */
    @Excel(name = "纬度")
    @HBaseColumn(family = "i", qualifier = "lat")
    private String latitude;
    /**
     * 经度
     */
    @Excel(name = "经度")
    @HBaseColumn(family = "i", qualifier = "long")
    private String longitude;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getUtcTime() {
        return utcTime;
    }

    public void setUtcTime(String utcTime) {
        this.utcTime = utcTime;
    }

    public String getLatitudeHemisphere() {
        return latitudeHemisphere;
    }

    public void setLatitudeHemisphere(String latitudeHemisphere) {
        this.latitudeHemisphere = latitudeHemisphere;
    }

    public String getLongitudeHemisphere() {
        return longitudeHemisphere;
    }

    public void setLongitudeHemisphere(String longitudeHemisphere) {
        this.longitudeHemisphere = longitudeHemisphere;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
}