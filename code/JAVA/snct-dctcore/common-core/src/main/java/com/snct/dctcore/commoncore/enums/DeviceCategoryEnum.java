package com.snct.dctcore.commoncore.enums;

/**
 * <br>
 * <b>功能描述:设备分类枚举</b>
 * <p>通用设备分类，用于对设备进行大类划分</p>
 *
 * <AUTHOR> Assistant
 */
public enum DeviceCategoryEnum implements IEnum {

    /**
     * 设备类型一
     */
    TYPE_1(1, "类型一"),
    
    /**
     * 设备类型二
     */
    TYPE_2(2, "类型二"),
    
    /**
     * 设备类型三
     */
    TYPE_3(3, "类型三");

    private Integer value;
    private String alias;

    DeviceCategoryEnum(int value, String alias) {
        this.value = value;
        this.alias = alias;
    }

    /**
     * 根据值获取枚举实例
     *
     * @param value 枚举值
     * @return 对应的枚举实例，如果未找到则返回null
     */
    public static DeviceCategoryEnum getByValue(int value) {
        for (DeviceCategoryEnum categoryEnum : DeviceCategoryEnum.values()) {
            if (categoryEnum.getValue().equals(value)) {
                return categoryEnum;
            }
        }
        return null;
    }

    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getAlias() {
        return alias;
    }
}
