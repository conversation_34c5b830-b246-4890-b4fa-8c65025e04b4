package com.snct.dctcore.commoncore.analysis;


import com.alibaba.fastjson2.JSONObject;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.ModemHbaseVo;
import com.snct.dctcore.commoncore.domain.hbase.PduHbaseVo;
import com.snct.dctcore.commoncore.snmp.SnmpResponse;
import com.snct.dctcore.commoncore.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * @ClassName: ModemAnalysis
 * @Description: 卫星猫数据解析
 * @author: wzewei
 * @date: 2025-09-05 10:48
 */
public class ModemAnalysis {

    private static final Logger logger = LoggerFactory.getLogger(ModemAnalysis.class);

    // OID定义 - 去掉前导点
    private static final String OID_SIGNAL = "*******.4.1.37576.*******1.1.4.1"; // 信号强度
    private static final String OID_SPEED = "*******.4.1.37576.*******1.1.1.1"; // 速度
    private static final String OID_SEND_POWER = "*******.4.1.37576.*******.0"; // 发送功率
    private static final String OID_STATUS = "*******.4.1.37576.*******.0"; // 状态标志

    /**
     * 获取卫星猫数据(NS300)
     *
     * @param kafkaMessage
     * @return
     */
    public static Object getModemData(KafkaMessage kafkaMessage) {

        if (kafkaMessage == null || kafkaMessage.getMsg() == null || kafkaMessage.getMsg().isEmpty()) {
            return null;
        }
        ModemHbaseVo modemHbaseVo = null;

        try {
            // 当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() :
                    kafkaMessage.getInitialTime();

            SnmpResponse snmpResponse = JSONObject.parseObject(kafkaMessage.getMsg(), SnmpResponse.class);

            if (snmpResponse != null) {
                modemHbaseVo = new ModemHbaseVo();
                modemHbaseVo.setInitialTime(Objects.requireNonNull(DateUtils.fetchWholeSecond(currentTime)).toString());
                modemHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime, "yyyy-MM-dd HH:mm:ss"));
                modemHbaseVo.setSignal(snmpResponse.getValue(OID_SIGNAL));
                modemHbaseVo.setSpeed(snmpResponse.getValue(OID_SPEED));
                modemHbaseVo.setSendPower(snmpResponse.getValue(OID_SEND_POWER));
                modemHbaseVo.setFlag(snmpResponse.getValue(OID_STATUS));
            }
        } catch (Exception e) {
            logger.error("解析卫星猫数据出错, 消息内容: [{}], 异常详情:", kafkaMessage.getMsg(), e);
        }
        return modemHbaseVo;
    }
}
