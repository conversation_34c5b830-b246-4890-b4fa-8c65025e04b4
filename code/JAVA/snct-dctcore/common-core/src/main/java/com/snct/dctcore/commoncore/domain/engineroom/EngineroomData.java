package com.snct.dctcore.commoncore.domain.engineroom;

import com.snct.dctcore.commoncore.enums.EngineroomDataParam;

/**
 * 机舱数据
 * <AUTHOR>
 */
public class EngineroomData {

    /**
     * 编号
     */
    private  String number;

    /**
     * 名称
     */
    private String name;
    /**
     *符号
     */
    private String symbol;

    /**
     * 状态
     */
    private String status;

    /**
     * value
     */
    private String value;

    /**
     * 时间
     */
    private Long timeStamp;

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public EngineroomData(){

    }

    public EngineroomData(String[] strbuff){
        this.number = strbuff[EngineroomDataParam.number.getValue()];
        this.name = strbuff[EngineroomDataParam.time.getValue()];
        this.symbol = strbuff[EngineroomDataParam.symbol.getValue()];
        this.status = strbuff[EngineroomDataParam.status.getValue()];
        this.value = strbuff[EngineroomDataParam.data.getValue()].trim();
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String mergeSendStr() {
        StringBuffer sb = new StringBuffer();
        sb.append(number == null ? " " : number).append(",");
        sb.append(name == null ? " " : name).append(",");
        sb.append(symbol == null ? " " : symbol).append(",");
        sb.append(status == null ? " " : status).append(",");
        sb.append(value == null ? " " : value);
        return sb.toString();
    }

    public void dataAnalysis(String dataStr){
        String[] split = dataStr.split(",");
        this.number = split[0];
        this.name = split[1];
        this.symbol = split[2];
        this.status = split[3];
        this.value = split[4];
    }
}
