package com.snct.dctcore.commoncore.analysis;

import com.snct.dctcore.commoncore.domain.KafkaMessage;

import java.util.Objects;

import static com.snct.dctcore.commoncore.enums.DeviceTypeEnum.getByValue;

/**
 * @ClassName: BaseAnalysis
 * @Description: 基础解析类
 * @author: wzewei
 * @date: 2025-09-05 09:28:24
 */
public class BaseAnalysis {

    /**
     * 解析数据
     *
     * @param kafkaMessage
     * @param object
     * @return
     */
    public static synchronized Object analysisData(KafkaMessage kafkaMessage, Object object, int  type) {
        // 根据Kafka消息类型进行不同的数据解析处理
        switch (Objects.requireNonNull(getByValue(kafkaMessage.getType()))) {
            case GPS:
                return GpsAnalysis.getGpsData(kafkaMessage);
            case ATTITUDE:
                return AttitudeAnalysis.getAttitudeData(kafkaMessage, type);
            case AWS:
                return AwsAnalysis.getAwsData(kafkaMessage, object);
            case PDU:
                return PduAnalysis.getPduData(kafkaMessage);
            case MODEM:
                return ModemAnalysis.getModemData(kafkaMessage);
            case AMPLIFIER:
                return AmplifierAnalysis.getAmplifierData(kafkaMessage);
            default:
                return null;
        }
    }

}
