package com.snct.dctcore.commoncore.constants;

/**
 * @description: 所有参数整合
 * @author: snct
 **/

public class RedisParameter {
    /*
     * @description: UDP消息接收参数
     */


    public static String SHIP_REPAIR_DATA_SET = "SHIP_REPAIR_DATA_SET";
    public static String SHIP_DO_SEND_LIST_COST = "SHIP_DO_SEND_LIST_COST-";
    public static String SHIP_DEVICE_HASH_DATA = "SHIP_DEVICE_HASH_DATA-";
    public static String PAZU_BANDWIDTH_LENGTH = "PAZU_BANDWIDTH_LENGTH";
    public static String PAZU_SEND_TOTAL_ONE_TIME = "PAZU_SEND_TOTAL_ONE_TIME";
    public static String SHIP_PACKAGE_DATA = "SHIP_PACKAGE_DATA-";
    public static String PAZU_PACKAGE_LENGTH = "PAZU_PACKAGE_LENGTH";
    public static String SHIP_IN_TRANSIT_NUM = "SHIP_IN_TRANSIT_NUM_";
    public static String SHIP_PACKAGE_NEWEST_NUM = "SHIP_PACKAGE_NEWEST_NUM_";
    public static String SHIP_PACKAGE_NUM_FILE_NAME = "SHIP_PACKAGE_NUM_FILE_NAME_";
    public static String SHIP_REPAIR_PACKAGE_DATA_SET = "SHIP_REPAIR_PACKAGE_DATA_SET_";
    public static int DEFAULT_SLEEP_TIME = 2;
    public static String SHIP_SYNC_SUCCESS = "SHIP_SYNC_SUCCESS_";
    public static String SHORE_SYNC_SUCCESS = "SHORE_SYNC_SUCCESS_";
    public static String SHIP_SN = "SHIP_SN_";

    /**
     * 记录不同船，pazu的ip和port,key中后面带上sn号
     */
    public static String SHIP_PAZU_IP = "SHIP_PAZU_IP_";
    public static String SHIP_PAZU_PORT = "SHIP_PAZU_PORT_";

    /**
     * 按数据类型存储，拆包的未合并之前临时存储
     */
    public static String SHORE_RECEIVE_CODE_TEMPORARY = "SHORE_RECEIVE_CODE_TEMPORARY_";

    /**
     * 存储接收到的数据
     */
    public static String SHORE_RECEIVE_DATA = "SHORE_RECEIVE_DATA-";

    /**
     * 拆包数据，最后存储时间
     */
    public static String SHORE_UNPACKING_NEWEST_TIME = "SHORE_UNPACKING_NEWEST_TIME_";

    /**
     * 岸上丢失数据集合
     */
    public static String SHORE_LOSE_DATA_SET = "SHORE_LOSE_DATA_SET";

    /**
     * 图片最新数据
     */
    public static String SHORE_UNPACKING_NEWEST_DATA = "SHORE_UNPACKING_NEWEST_DATA_";


    /**
     * 最大接收到的编号--new
     */
    public static String MAX_RECEIVE_NUM = "MAX_RECEIVE_NUM-";
    /**
     * 最大校验编号--new
     */
    public static String MAX_CHECK_NUM = "MAX_CHECK_NUM-";
    /**
     * 岸上丢失数据链表--new
     */
    public static String LOSE_DATA_LIST = "LOSE_DATA_LIST-";
    /**
     * 最大接收编号与最大校验编号的差--new
     */
    public static String CHECK_INTERVAL_NUM = "CHECK_INTERVAL_NUM-";
    /**
     * 所有启用的船只sn号
     */
    public static String ALL_ENABLE_SHIP_SN = "ALL_ENABLE_SHIP_SN";

    /**
     * 最大接收编号
     */
    public static String SHORE_MAX_RECEIVE_NUM = "SHORE_MAX_RECEIVE_NUM";

    /**
     * 最大校验编号
     */
    public static String SHORE_MAX_CHECK_NUM = "SHORE_MAX_CHECK_NUM";

    /**
     * 最大接收编号与最大校验编号的差
     */
    public static String SHORE_CHECK_INTERVAL_NUM = "SHORE_CHECK_INTERVAL_NUM";

    /*
     * @description:UDP丢包补数据服务
     */
    /**
     * 岸上丢失数据集合--已发送过一次(记录起来，等N分钟后再发)
     */
    public static String SHORE_LOSE_DATA_NUM_HASH = "SHORE_LOSE_DATA_NUM_HASH";

    /**
     * redis数据最新时间
     */
    public static String LATEST_DATA = "LATEST_DATA_";

    /**
     * redis数据最新时间
     */
    public static String LATEST_DATE = "LATEST_DATE_";

    /**
     * 单位时间内接收到的数据的数量，丢失重传的数量
     */
    public static String DATA_AMOUNT = "DATA_AMOUNT-";




//---------------------------------------------数据接收日志----------------------------------------------------------------
    /**
     * 一秒钟接收数据总长度
     */
    public static String RECEIVE_TOTAL_LENGTH_S = "RECEIVE_TOTAL_LENGTH_S-";
    /**
     * 一秒钟接收数据总条数
     */
    public static String RECEIVE_LINES_S = "RECEIVE_LINES_S-";
    /**
     * 一秒钟接收补数据总长度
     */
    public static String RECEIVE_REPAIR_LENGTH_S = "RECEIVE_REPAIR_LENGTH_S-";
    /**
     * 一秒钟接收图片数据长度
     */
    public static String RECEIVE_PIC_LENGTH_S = "RECEIVE_PIC_LENGTH_S-";

    /**
     * 一分钟接收数据总长度
     */
    public static String RECEIVE_TOTAL_LENGTH_M = "RECEIVE_TOTAL_LENGTH_M-";
    /**
     * 一分钟接收数据总条数
     */
    public static String RECEIVE_LINES_M = "RECEIVE_LINES_M-";
    /**
     * 一分钟接收补数据总长度
     */
    public static String RECEIVE_REPAIR_LENGTH_M = "RECEIVE_REPAIR_LENGTH_M-";
    /**
     * 一分钟接收图片数据长度
     */
    public static String RECEIVE_PIC_LENGTH_M = "RECEIVE_PIC_LENGTH_M-";
    /**
     * 一分钟接收数据长度,是否已保存
     */
    public static String RECEIVE_DATA_LENGTH_SAVE = "RECEIVE_DATA_LENGTH_SAVE-";

    /**
     * 最新接收图片的时间
     */
    public static String LATEST_PICTURE_DATE = "LATEST_PICTURE_DATE-";

//---------------------------------------------数据接收日志----------------------------------------------------------------

//---------------------------------------------船舶----------------------------------------------------------------
    /**
     * 船舶最新位置
     */
    public static String SHIP_LOCATION_LATEST = "SHIP:LOCATION:LATEST:";

    /**
     * 所有船舶最新位置
     */
    public static String SHIP_LOCATION_ALL = "SHIP:LOCATION:ALL";

    /**
     * 船舶最新位置GEO
     */
    public static final String SHIP_LOCATION_GEO = "SHIP:LOCATION:GEO";

    /**
     * 船舶预警
     */
    public static String SHIP_WARNING = "SHIP:WARNING:";

//---------------------------------------------船舶----------------------------------------------------------------

//---------------------------------------------设备----------------------------------------------------------------
    /**
     * 设备列表
     */
    public static String DEVICE_LIST = "DEVICE:List-";

    /**
     * 设备数据
     */
    public static String DEVICE_DATA = "DEVICE:DATA:";

    /**
     * 设备最新时间戳
     */
    public static final String DEVICE_LATEST_TIME = "DEVICE:TIME:";

//---------------------------------------------设备----------------------------------------------------------------

//---------------------------------------------台风----------------------------------------------------------------

    /**
     * 台风列表
     */
    public static String TYPHOON_LIST = "TYPHOON:List-";
    /**
     * 台风信息列表
     */
    public static String TYPHOON_INFO = "TYPHOON:INFO-";

    /**
     * 活跃台风摘要信息
     */
    public static final String TYPHOON_ACTIVE_SUMMARY = "TYPHOON:ACTIVE";

    /**
     * 台风预警
     */
    public static final String TYPHOON_WARNING = "TYPHOON:WARNING:";

    /**
     * 台风预警摘要信息
     */
    public static final String TYPHOON_WARNING_SUMMARY = "TYPHOON:WARNING:SUMMARY";



//---------------------------------------------台风----------------------------------------------------------------

//---------------------------------------------机舱数据----------------------------------------------------------------

    /**
     * 机舱数据接收时间
     */
    public static String ENGINE_ROOM_DATE = "ENGINE_ROOM_DATE";

//---------------------------------------------机舱数据----------------------------------------------------------------
}
