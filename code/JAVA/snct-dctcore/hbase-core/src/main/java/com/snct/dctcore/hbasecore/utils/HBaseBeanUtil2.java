package com.snct.dctcore.hbasecore.utils;


import com.snct.dctcore.commoncore.annotation.HBaseColumn;
import com.snct.dctcore.commoncore.domain.hbase.old.HbaseColumnVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;

/**
 * @ClassName: HbaseBeanUtil2
 * @Description: TODO
 * @author: wzewei
 * @date: 2025-08-15 14:08
 */
public class HBaseBeanUtil2 {

    private static final Logger logger = LoggerFactory.getLogger(HBaseBeanUtil.class);

    /**
     * JavaBean转换为Put
     *
     * @param <T>
     * @param obj
     * @return
     * @throws Exception
     */
    public static <T> Put beanToPut(T obj) throws Exception {
        Put put = new Put(Bytes.toBytes(parseObjId(obj)));
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (!field.isAnnotationPresent(HBaseColumn.class)) {
                continue;
            }
            field.setAccessible(true);
            HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
            String family = orm.family();
            String qualifier = orm.qualifier();
            if (StringUtils.isBlank(family) || StringUtils.isBlank(qualifier)) {
                continue;
            }
            if ("rowkey".equalsIgnoreCase(qualifier) || "rowkey".equalsIgnoreCase(family)) {
                continue;
            }
            if (field.get(obj) == null) {
                continue;
            }
            if (field.get(obj) != null || StringUtils.isNotBlank(field.get(obj).toString())) {
                put.addColumn(Bytes.toBytes(family), Bytes.toBytes(qualifier), Bytes.toBytes(field.get(obj).toString()));
            }
        }
        return put;
    }

    /**
     * 获取rowkey
     *
     * @param columnMap
     * @return
     */
    private static String getObjId(Map<String, HbaseColumnVo> columnMap) {
        for (Map.Entry<String, HbaseColumnVo> cm : columnMap.entrySet()) {
            if (cm.getValue() == null) {
                continue;
            }
            if ("rowkey".equalsIgnoreCase(cm.getValue().getQualifier()) && "rowkey".equalsIgnoreCase(cm.getValue().getFamily())) {
                return cm.getValue().getValue();
            }
        }
        return "";
    }

    /**
     * 获取Bean中的id,作为Rowkey
     *
     * @param <T>
     * @param obj
     * @return
     */
    public static <T> String parseObjId(T obj) {
        Class<?> clazz = obj.getClass();
        try {
            Field field = clazz.getDeclaredField("id");
            field.setAccessible(true);
            Object object = field.get(obj);
            return object.toString();
        } catch (NoSuchFieldException e) {
            logger.error("", e);
        } catch (SecurityException e) {
            logger.error("", e);
        } catch (IllegalArgumentException e) {
            logger.error("", e);
        } catch (IllegalAccessException e) {
            logger.error("", e);
        }
        return "";
    }

    /**
     * HBase result 转换为 bean
     *
     * @param <T>
     * @param result
     * @param obj
     * @return
     * @throws Exception
     */
    public static <T> T resultToBean(Result result, T obj) throws Exception {
        if (result == null) {
            return null;
        }

        Map<String, HbaseColumnVo> columnMap = getObjColumnMap(obj);
        return setValue2Bean(obj, columnMap, result);
    }

    /**
     * @param result
     * @param family
     * @param qualifier
     * @param timeStamp
     * @return
     */
    private static String getResultValueByType(Result result, String family, String qualifier, boolean timeStamp) {
        if (!timeStamp) {
            byte[] bytes = result.getValue(Bytes.toBytes(family), Bytes.toBytes(qualifier));
            return bytes == null ? null : new String(bytes);
        }
        List<Cell> cells = result.getColumnCells(Bytes.toBytes(family), Bytes.toBytes(qualifier));
        if (cells.size() == 1) {
            Cell cell = cells.get(0);
            return cell.getTimestamp() + "";
        }
        return "";
    }

    /**
     * 批量转换 Result -> Bean
     *
     * @param results
     * @param obj
     * @param <T>
     * @return
     * @throws Exception
     */
    static <T> List<T> resultToBean(List<Result> results, T obj, List<T> objs) throws Exception {
        if (results == null) {
            return objs;
        }
        Map<String, HbaseColumnVo> columnMap = getObjColumnMap(obj);

        for (Result result : results) {
            objs.add(setValue2Bean(obj, columnMap, result));
        }

        return objs;
    }

    /**
     * 获取对象的set方法
     *
     * @param obj
     * @param <T>
     * @return
     * @throws NoSuchMethodException
     */
    private static <T> Map<String, HbaseColumnVo> getObjColumnMap(T obj) throws NoSuchMethodException, IllegalAccessException {
        Map<String, HbaseColumnVo> columnInfoMap = new HashMap<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        HbaseColumnVo hbaseColumnVo;

        for (Field field : fields) {
            if (!field.isAnnotationPresent(HBaseColumn.class)) {
                continue;
            }
            hbaseColumnVo = new HbaseColumnVo();

            field.setAccessible(true);
            HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
            String family = orm.family();
            String qualifier = orm.qualifier();
            if (StringUtils.isBlank(family) || StringUtils.isBlank(qualifier)) {
                continue;
            }
            hbaseColumnVo.setFamily(family);
            hbaseColumnVo.setQualifier(qualifier);

            String fieldName = field.getName();
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String setMethodName = "set" + firstLetter + fieldName.substring(1);
            Method setMethod = clazz.getMethod(setMethodName, new Class[]{field.getType()});
            hbaseColumnVo.setSetMethod(setMethod);

            if (field.get(obj) != null && StringUtils.isNotBlank(field.get(obj).toString())) {
                hbaseColumnVo.setValue(field.get(obj).toString());
            }

            columnInfoMap.put(qualifier, hbaseColumnVo);
        }

        return columnInfoMap;
    }

    /**
     * 获取Result中的对应的值
     *
     * @param result
     * @return
     */
    private static Map<String, String> getResultValueMap(Result result) {
        NavigableMap<byte[], NavigableMap<byte[], byte[]>> noVersionMap = result.getNoVersionMap();
        if (noVersionMap == null) {
            return null;
        }

        NavigableMap<byte[], byte[]> map = noVersionMap.firstEntry().getValue();
        Map<String, String> valueMap = new HashMap<>(map.size());
        for (byte[] key : map.keySet()) {
            valueMap.put(new String(key), new String(map.get(key)));
        }
        return valueMap;
    }

    /**
     * 更新对象的值
     *
     * @param obj
     * @param columnMap
     * @param result
     * @param <T>
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    private static <T> T setValue2Bean(T obj, Map<String, HbaseColumnVo> columnMap, Result result) throws InvocationTargetException, IllegalAccessException, InstantiationException {
        Map<String, String> valueMap = getResultValueMap(result);
        if (valueMap == null || valueMap.size() == 0) {
            return null;
        }

        Class<?> clazz = obj.getClass();
        T beanClone = (T) clazz.newInstance();

        for (Map.Entry<String, HbaseColumnVo> cm : columnMap.entrySet()) {
            if (valueMap.get(cm.getKey()) == null) {
                continue;
            }
            if (cm.getValue() == null) {
                continue;
            }
            cm.getValue().getSetMethod().invoke(beanClone, new Object[]{valueMap.get(cm.getKey())});
        }
        return beanClone;
    }
}
